const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 配置加载器
 * 负责加载和验证 BuildFixer 配置
 */
class ConfigLoader {
  constructor() {
    this.defaultConfig = {
      buildCommand: 'npm run build',
      installCommand: 'npm install',
      maxAttempts: 3,
      useLegacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      verbose: false,
      showProgress: true,
      errorPatterns: {
        typescript: [
          'error TS\\d+:',
          '\\.ts\\(\\d+,\\d+\\):'
        ],
        vue: [
          '\\.vue:\\d+:\\d+:',
          'Vue warn'
        ],
        webpack: [
          'ERROR in',
          'Module not found'
        ],
        eslint: [
          '\\d+:\\d+\\s+(error|warning)'
        ]
      },
      fixStrategies: {
        'missing-module': {
          enabled: true,
          priority: 1,
          useAI: false
        },
        'property-not-exist': {
          enabled: true,
          priority: 2,
          useAI: true
        },
        'vue-version': {
          enabled: true,
          priority: 3,
          useAI: true
        },
        'ui-library': {
          enabled: true,
          priority: 4,
          useAI: true
        }
      },
      aiConfig: {
        maxTokens: 8000,
        temperature: 0.0,
        maxRetries: 3,
        timeout: 30000
      },
      moduleMapping: {
        'vue': 'vue@^3.0.0',
        'vue-router': 'vue-router@^4.0.0',
        'vuex': '@pinia/nuxt',
        'element-ui': 'element-plus',
        'vue-template-compiler': '@vue/compiler-sfc'
      },
      excludePatterns: [
        'node_modules/**',
        'dist/**',
        'build/**',
        '*.min.js',
        '*.map'
      ],
      backupConfig: {
        enabled: true,
        suffix: 'build-fixer-backup',
        maxBackups: 5
      },
      logging: {
        level: 'info',
        logFile: 'build-fixer.log',
        enableFileLogging: false
      }
    };
  }

  /**
   * 加载配置
   */
  async loadConfig(configPath, projectPath) {
    let config = { ...this.defaultConfig };

    // 首先读取项目的 package.json 来获取构建命令
    const packageConfig = await this.loadPackageJsonConfig(projectPath);
    if (packageConfig) {
      config = this.mergeConfig(config, packageConfig);
    }

    // 如果指定了配置文件路径
    if (configPath) {
      const userConfig = await this.loadConfigFile(configPath);
      if (userConfig) {
        config = this.mergeConfig(config, userConfig);
      }
    } else {
      // 查找项目中的配置文件
      const projectConfig = await this.findProjectConfig(projectPath);
      if (projectConfig) {
        config = this.mergeConfig(config, projectConfig);
      }
    }

    // 验证配置
    this.validateConfig(config);

    return config;
  }

  /**
   * 从项目的 package.json 加载配置
   */
  async loadPackageJsonConfig(projectPath) {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        console.warn(chalk.yellow(`⚠️  package.json 不存在: ${packageJsonPath}`));
        return null;
      }

      const packageJson = await fs.readJson(packageJsonPath);
      const scripts = packageJson.scripts || {};

      // 检测包管理器
      const packageManager = this.detectPackageManager(projectPath);

      // 调试信息（仅在 verbose 模式下显示）
      if (process.env.VERBOSE) {
        console.log(chalk.gray('调试: 检测到的脚本:'), Object.keys(scripts));
        console.log(chalk.gray('调试: 包管理器:'), packageManager);
      }

      // 根据 scripts 推断命令
      const config = {
        installCommand: `${packageManager} install`
      };

      // 检测构建命令 - 优先使用生产构建
      if (scripts['build:prod']) {
        config.buildCommand = `${packageManager} run build:prod`;
        if (process.env.VERBOSE) console.log(chalk.gray('调试: 使用 build:prod'));
      } else if (scripts['build:production']) {
        config.buildCommand = `${packageManager} run build:production`;
        if (process.env.VERBOSE) console.log(chalk.gray('调试: 使用 build:production'));
      } else if (scripts.build) {
        config.buildCommand = `${packageManager} run build`;
        if (process.env.VERBOSE) console.log(chalk.gray('调试: 使用 build'));
      }

      // 检测开发命令
      if (scripts.dev) {
        config.devCommand = `${packageManager} run dev`;
      } else if (scripts.serve) {
        config.devCommand = `${packageManager} run serve`;
      } else if (scripts.start) {
        config.devCommand = `${packageManager} run start`;
      }

      console.log(chalk.green(`✅ 已从 package.json 加载配置: ${packageManager}`));
      if (process.env.VERBOSE) console.log(chalk.gray('调试: 最终配置:'), config);
      return config;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  读取 package.json 失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 检测包管理器
   */
  detectPackageManager(projectPath) {
    // 检查锁文件来确定包管理器
    if (fs.existsSync(path.join(projectPath, 'pnpm-lock.yaml'))) {
      return 'pnpm';
    } else if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) {
      return 'yarn';
    } else {
      return 'npm';
    }
  }

  /**
   * 加载指定的配置文件
   */
  async loadConfigFile(configPath) {
    try {
      const fullPath = path.resolve(configPath);
      if (!await fs.pathExists(fullPath)) {
        console.warn(chalk.yellow(`⚠️  配置文件不存在: ${fullPath}`));
        return null;
      }

      const config = await fs.readJson(fullPath);
      console.log(chalk.green(`✅ 已加载配置文件: ${fullPath}`));
      return config;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  配置文件格式错误: ${configPath}`));
      console.warn(chalk.gray(`   错误: ${error.message}`));
      return null;
    }
  }

  /**
   * 查找项目中的配置文件
   */
  async findProjectConfig(projectPath) {
    const configFiles = [
      'build-fixer.config.json',
      '.build-fixer.json',
      'build-fixer.json',
      '.build-fixer.config.json'
    ];

    for (const configFile of configFiles) {
      const configFilePath = path.join(projectPath, configFile);
      if (await fs.pathExists(configFilePath)) {
        try {
          const config = await fs.readJson(configFilePath);
          console.log(chalk.green(`✅ 已加载项目配置: ${configFile}`));
          return config;
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  配置文件格式错误: ${configFile}`));
          console.warn(chalk.gray(`   错误: ${error.message}`));
        }
      }
    }

    return null;
  }

  /**
   * 合并配置
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };

    // 深度合并对象属性
    for (const [key, value] of Object.entries(userConfig)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        merged[key] = { ...merged[key], ...value };
      } else {
        merged[key] = value;
      }
    }

    return merged;
  }

  /**
   * 验证配置
   */
  validateConfig(config) {
    const errors = [];

    // 验证必需的字符串字段
    const requiredStrings = ['buildCommand', 'installCommand'];
    for (const field of requiredStrings) {
      if (!config[field] || typeof config[field] !== 'string') {
        errors.push(`${field} 必须是非空字符串`);
      }
    }

    // 验证数字字段
    if (!Number.isInteger(config.maxAttempts) || config.maxAttempts < 1 || config.maxAttempts > 10) {
      errors.push('maxAttempts 必须是 1-10 之间的整数');
    }

    // 验证 AI 配置
    if (config.aiConfig) {
      const { maxTokens, temperature, maxRetries, timeout } = config.aiConfig;
      
      if (maxTokens && (!Number.isInteger(maxTokens) || maxTokens < 1000 || maxTokens > 32000)) {
        errors.push('aiConfig.maxTokens 必须是 1000-32000 之间的整数');
      }
      
      if (temperature !== undefined && (typeof temperature !== 'number' || temperature < 0 || temperature > 2)) {
        errors.push('aiConfig.temperature 必须是 0-2 之间的数字');
      }
      
      if (maxRetries && (!Number.isInteger(maxRetries) || maxRetries < 1 || maxRetries > 10)) {
        errors.push('aiConfig.maxRetries 必须是 1-10 之间的整数');
      }
      
      if (timeout && (!Number.isInteger(timeout) || timeout < 5000 || timeout > 300000)) {
        errors.push('aiConfig.timeout 必须是 5000-300000 之间的整数');
      }
    }

    if (errors.length > 0) {
      console.error(chalk.red('❌ 配置验证失败:'));
      errors.forEach(error => console.error(chalk.red(`   - ${error}`)));
      throw new Error('配置验证失败');
    }
  }

  /**
   * 创建默认配置文件
   */
  async createDefaultConfig(projectPath) {
    const configPath = path.join(projectPath, 'build-fixer.config.json');
    
    if (await fs.pathExists(configPath)) {
      console.log(chalk.yellow('⚠️  配置文件已存在，跳过创建'));
      return configPath;
    }

    await fs.writeJson(configPath, this.defaultConfig, { spaces: 2 });
    console.log(chalk.green(`✅ 默认配置文件已创建: ${configPath}`));
    return configPath;
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return { ...this.defaultConfig };
  }
}

module.exports = ConfigLoader;

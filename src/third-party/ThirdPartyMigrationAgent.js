const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../ai/ai-service');
const ToolExecutor = require('../services/tools/ToolExecutor');
const PromptBuilder = require('../services/ai/PromptBuilder');
const ComponentSearcher = require('./ComponentSearcher');
const MigrationRuleEngine = require('./MigrationRuleEngine');

/**
 * ThirdPartyMigrationAgent - Vue 2 到 Vue 3 第三方组件迁移代理
 * 
 * 核心功能：
 * - 智能识别项目中使用的第三方组件
 * - 根据文件大小和复杂度选择迁移策略
 * - 使用AI Agent模式进行两阶段迁移
 * - 提供成本优化的迁移方案
 */
class ThirdPartyMigrationAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'migration-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxFileSize: 200, // 200行以内直接AI翻译
      maxAttempts: 3,
      dryRun: false,
      verbose: false,
      useRipgrep: true,
      ...options
    };

    // 初始化工具执行器
    this.toolExecutor = new ToolExecutor(this.projectPath, {
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    // 初始化提示词构建器
    this.promptBuilder = new PromptBuilder(this.toolExecutor, {
      maxOutputLength: 8000,
      includeContext: true
    });

    // 初始化组件搜索器
    this.componentSearcher = new ComponentSearcher(this.projectPath, {
      useRipgrep: this.options.useRipgrep,
      verbose: this.options.verbose
    });

    // 初始化迁移规则引擎
    this.migrationRuleEngine = new MigrationRuleEngine({
      verbose: this.options.verbose
    });

    // 迁移统计
    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      aiMigratedFiles: 0,
      ruleMigratedFiles: 0,
      skippedFiles: 0,
      errors: []
    };
  }

  /**
   * 执行完整的第三方组件迁移流程
   */
  async migrate() {
    try {
      console.log(chalk.blue('🚀 开始Vue 2到Vue 3第三方组件迁移...'));
      console.log(chalk.gray(`项目路径: ${this.projectPath}`));

      // 1. 分析项目中的第三方组件使用情况
      const analysisResult = await this.analyzeThirdPartyUsage();
      
      if (analysisResult.components.length === 0) {
        console.log(chalk.green('✅ 未发现需要迁移的第三方组件'));
        return this.createMigrationResult(true, '无需迁移');
      }

      // 2. 按策略处理每个组件的使用文件
      for (const component of analysisResult.components) {
        await this.migrateComponent(component);
      }

      // 3. 生成迁移报告
      this.generateMigrationReport();

      const success = this.stats.errors.length === 0;
      const message = success ? '迁移完成' : `迁移完成，但有 ${this.stats.errors.length} 个错误`;
      
      return this.createMigrationResult(success, message);
    } catch (error) {
      console.error(chalk.red('❌ 迁移过程中发生错误:'), error.message);
      return this.createMigrationResult(false, error.message);
    }
  }

  /**
   * 分析项目中的第三方组件使用情况
   */
  async analyzeThirdPartyUsage() {
    console.log(chalk.gray('🔍 分析第三方组件使用情况...'));
    
    // 使用组件搜索器查找所有相关文件
    const searchResult = await this.componentSearcher.searchAllComponents();
    
    console.log(chalk.gray(`找到 ${searchResult.components.length} 个需要迁移的组件`));
    console.log(chalk.gray(`涉及 ${searchResult.totalFiles} 个文件`));
    
    this.stats.totalFiles = searchResult.totalFiles;
    
    return searchResult;
  }

  /**
   * 迁移单个组件的所有使用文件
   */
  async migrateComponent(component) {
    console.log(chalk.blue(`\n📦 处理组件: ${component.name}`));
    console.log(chalk.gray(`  目标: ${component.target || '未知'}`));
    console.log(chalk.gray(`  文件数: ${component.files.length}`));

    for (const fileInfo of component.files) {
      await this.migrateFile(fileInfo, component);
    }
  }

  /**
   * 迁移单个文件 - 核心迁移逻辑
   */
  async migrateFile(fileInfo, component) {
    const relativePath = path.relative(this.projectPath, fileInfo.path);
    
    try {
      console.log(chalk.gray(`  🔧 处理文件: ${relativePath}`));

      // 读取文件内容
      const fileResult = await this.toolExecutor.executeToolCall('read_file', { 
        file_path: relativePath 
      });

      if (!fileResult.success) {
        throw new Error(`无法读取文件: ${fileResult.error}`);
      }

      const fileContent = fileResult.content;
      const lineCount = fileContent.split('\n').length;

      // 根据文件大小选择迁移策略
      let migrationResult;
      if (lineCount <= this.options.maxFileSize) {
        // 小文件：直接AI翻译
        migrationResult = await this.migrateWithAI(fileInfo, component, fileContent);
      } else {
        // 大文件：使用两阶段AI模式
        migrationResult = await this.migrateWithTwoPhaseAI(fileInfo, component, fileContent);
      }

      if (migrationResult.success) {
        this.stats.processedFiles++;
        if (migrationResult.method === 'ai') {
          this.stats.aiMigratedFiles++;
        } else if (migrationResult.method === 'rule') {
          this.stats.ruleMigratedFiles++;
        }
        console.log(chalk.green(`    ✅ 迁移成功 (${migrationResult.method})`));
      } else {
        this.stats.skippedFiles++;
        console.log(chalk.yellow(`    ⚠️  跳过: ${migrationResult.reason}`));
      }

    } catch (error) {
      this.stats.errors.push({
        file: relativePath,
        component: component.name,
        error: error.message
      });
      console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`));
    }
  }

  /**
   * 使用AI直接翻译小文件
   */
  async migrateWithAI(fileInfo, component, fileContent) {
    try {
      // 首先尝试规则引擎
      const ruleResult = this.migrationRuleEngine.applyRules(fileContent, component);
      if (ruleResult.success && ruleResult.hasChanges) {
        // 规则引擎成功处理
        await this.writeFile(fileInfo.path, ruleResult.content);
        return { success: true, method: 'rule' };
      }

      // 规则引擎无法处理，使用AI
      if (!this.enabled) {
        return { success: false, reason: 'AI服务未启用且规则引擎无法处理' };
      }

      const prompt = this.buildDirectMigrationPrompt(fileInfo, component, fileContent);
      const aiResponse = await this.callAI(prompt, {
        context: {
          taskType: 'direct-migration',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name
        }
      });

      const migratedContent = this.extractCodeFromResponse(aiResponse);
      if (migratedContent) {
        await this.writeFile(fileInfo.path, migratedContent);
        return { success: true, method: 'ai' };
      }

      return { success: false, reason: 'AI返回无效内容' };
    } catch (error) {
      return { success: false, reason: error.message };
    }
  }

  /**
   * 使用两阶段AI模式处理大文件
   */
  async migrateWithTwoPhaseAI(fileInfo, component, fileContent) {
    try {
      // 第一阶段：分析文件，确定需要修改的位置
      const analysisResult = await this.analyzeFileForMigration(fileInfo, component, fileContent);
      
      if (!analysisResult.success) {
        return { success: false, reason: `分析失败: ${analysisResult.error}` };
      }

      // 第二阶段：基于分析结果生成具体修改
      const migrationResult = await this.generateMigrationChanges(
        fileInfo, component, fileContent, analysisResult.changes
      );

      if (migrationResult.success) {
        await this.writeFile(fileInfo.path, migrationResult.content);
        return { success: true, method: 'ai' };
      }

      return { success: false, reason: migrationResult.error };
    } catch (error) {
      return { success: false, reason: error.message };
    }
  }

  /**
   * 第一阶段：分析文件中需要迁移的位置
   */
  async analyzeFileForMigration(fileInfo, component, fileContent) {
    try {
      const prompt = this.buildAnalysisPrompt(fileInfo, component, fileContent);
      
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'file-analysis',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name,
          phase: 'analysis'
        }
      });

      return this.parseAnalysisResponse(response);
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 第二阶段：基于分析结果生成迁移代码
   */
  async generateMigrationChanges(fileInfo, component, fileContent, changes) {
    try {
      const prompt = this.buildMigrationPrompt(fileInfo, component, fileContent, changes);
      
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'migration-generation',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name,
          phase: 'generation'
        }
      });

      const migratedContent = this.extractCodeFromResponse(response);
      if (migratedContent) {
        return { success: true, content: migratedContent };
      }

      return { success: false, error: 'AI返回无效内容' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 写入文件（支持备份和dry-run）
   */
  async writeFile(filePath, content) {
    if (this.options.dryRun) {
      console.log(chalk.gray(`    📝 [DRY-RUN] 将写入文件: ${path.relative(this.projectPath, filePath)}`));
      return;
    }

    // 创建备份
    const backupPath = filePath + '.backup';
    if (!await fs.pathExists(backupPath)) {
      await fs.copy(filePath, backupPath);
    }

    // 写入新内容
    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * 创建迁移结果
   */
  createMigrationResult(success, message) {
    return {
      success,
      message,
      stats: { ...this.stats }
    };
  }

  /**
   * 生成迁移报告
   */
  generateMigrationReport() {
    console.log(chalk.blue('\n📊 第三方组件迁移报告:'));
    console.log(`总文件数: ${this.stats.totalFiles}`);
    console.log(`处理文件数: ${this.stats.processedFiles}`);
    console.log(`AI迁移: ${this.stats.aiMigratedFiles}`);
    console.log(`规则迁移: ${this.stats.ruleMigratedFiles}`);
    console.log(`跳过文件: ${this.stats.skippedFiles}`);

    if (this.stats.errors.length > 0) {
      console.log(chalk.red(`错误数: ${this.stats.errors.length}`));
      this.stats.errors.forEach(error => {
        console.log(chalk.red(`  - ${error.file}: ${error.error}`));
      });
    }
  }

  /**
   * 构建直接迁移提示词（小文件）
   */
  buildDirectMigrationPrompt(fileInfo, component, fileContent) {
    return `你是一个Vue 2到Vue 3的迁移专家。请将以下代码中的第三方组件 "${component.name}" 迁移到 "${component.target || 'Vue 3兼容版本'}"。

迁移规则：
1. 更新import语句
2. 调整组件注册方式（如果需要）
3. 修改API调用（根据新版本文档）
4. 保持原有功能不变
5. 确保代码风格一致

${component.migrationGuide ? `迁移指南：\n${component.migrationGuide}\n` : ''}

原始代码：
\`\`\`${path.extname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请提供完整的迁移后代码，只返回代码内容，不要包含解释文字。`;
  }

  /**
   * 构建文件分析提示词（大文件第一阶段）
   */
  buildAnalysisPrompt(fileInfo, component, fileContent) {
    const lines = fileContent.split('\n');
    const totalLines = lines.length;

    return `你是一个Vue 2到Vue 3的迁移专家。请分析以下代码文件，找出所有需要迁移的 "${component.name}" 相关代码位置。

文件信息：
- 文件路径: ${path.relative(this.projectPath, fileInfo.path)}
- 总行数: ${totalLines}
- 目标组件: ${component.target || 'Vue 3兼容版本'}

请仔细分析代码，找出：
1. import/require语句的位置
2. 组件注册的位置
3. 组件使用的位置
4. 相关配置的位置

代码内容：
\`\`\`${path.extname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请以JSON格式返回分析结果：
{
  "changes": [
    {
      "type": "import|register|usage|config",
      "startLine": 行号,
      "endLine": 行号,
      "description": "需要修改的描述",
      "originalCode": "原始代码",
      "priority": "high|medium|low"
    }
  ]
}`;
  }

  /**
   * 构建迁移代码生成提示词（大文件第二阶段）
   */
  buildMigrationPrompt(fileInfo, component, fileContent, changes) {
    return `你是一个Vue 2到Vue 3的迁移专家。基于之前的分析结果，请生成迁移后的完整代码。

文件信息：
- 文件路径: ${path.relative(this.projectPath, fileInfo.path)}
- 组件: ${component.name} → ${component.target || 'Vue 3兼容版本'}

需要修改的位置：
${changes.map(change => `- 第${change.startLine}-${change.endLine}行: ${change.description}`).join('\n')}

${component.migrationGuide ? `迁移指南：\n${component.migrationGuide}\n` : ''}

原始代码：
\`\`\`${path.extname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请提供完整的迁移后代码，确保：
1. 所有标识的位置都已正确修改
2. 保持原有功能不变
3. 代码风格一致
4. 语法正确

只返回代码内容，不要包含解释文字。`;
  }

  /**
   * 解析分析响应
   */
  parseAnalysisResponse(response) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return { success: false, error: '无法找到JSON格式的分析结果' };
      }

      const analysisData = JSON.parse(jsonMatch[0]);

      if (!analysisData.changes || !Array.isArray(analysisData.changes)) {
        return { success: false, error: '分析结果格式不正确' };
      }

      return {
        success: true,
        changes: analysisData.changes
      };
    } catch (error) {
      return { success: false, error: `解析分析结果失败: ${error.message}` };
    }
  }

  /**
   * 从AI响应中提取代码
   */
  extractCodeFromResponse(response) {
    try {
      // 尝试提取代码块
      const codeBlockMatch = response.match(/```[\w]*\n([\s\S]*?)\n```/);
      if (codeBlockMatch) {
        return codeBlockMatch[1];
      }

      // 如果没有代码块标记，检查是否整个响应都是代码
      const lines = response.split('\n');
      const nonCodeLines = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed &&
               !trimmed.startsWith('//') &&
               !trimmed.startsWith('/*') &&
               !trimmed.startsWith('*') &&
               !trimmed.startsWith('import') &&
               !trimmed.startsWith('export') &&
               !trimmed.startsWith('<') &&
               !trimmed.includes('function') &&
               !trimmed.includes('const') &&
               !trimmed.includes('let') &&
               !trimmed.includes('var') &&
               !/^[\s\w\(\)\{\}\[\];,\.=\-\+\*\/\%\!\?\:]+$/.test(trimmed);
      });

      // 如果大部分内容看起来像代码，直接返回
      if (nonCodeLines.length < lines.length * 0.1) {
        return response.trim();
      }

      return null;
    } catch (error) {
      console.warn(chalk.yellow(`提取代码失败: ${error.message}`));
      return null;
    }
  }
}

module.exports = ThirdPartyMigrationAgent;

const path = require('path');
const chalk = require('chalk');
const ThirdPartyMigrationAgent = require('./ThirdPartyMigrationAgent');

/**
 * 第三方组件迁移集成模块
 * 
 * 提供与现有迁移工具的集成接口
 * 可以作为迁移流程中的一个步骤
 */

/**
 * 集成到AutoMigrator的第三方组件迁移步骤
 */
async function integrateWithAutoMigrator(projectPath, options = {}) {
  console.log(chalk.blue('🔗 执行第三方组件迁移步骤'));
  
  const migrationOptions = {
    maxFileSize: 200,
    maxAttempts: 3,
    dryRun: options.dryRun || false,
    verbose: options.verbose || false,
    useRipgrep: options.useRipgrep !== false,
    apiKey: options.aiApiKey || process.env.AI_API_KEY,
    ...options
  };

  try {
    const agent = new ThirdPartyMigrationAgent(projectPath, migrationOptions);
    const result = await agent.migrate();

    return {
      success: result.success,
      message: result.message,
      stats: result.stats,
      stepName: 'third-party-migration',
      recommendations: generateRecommendations(result)
    };
  } catch (error) {
    return {
      success: false,
      message: `第三方组件迁移失败: ${error.message}`,
      error: error,
      stepName: 'third-party-migration'
    };
  }
}

/**
 * 生成迁移建议
 */
function generateRecommendations(migrationResult) {
  const recommendations = [];

  if (!migrationResult.success) {
    recommendations.push({
      type: 'error',
      message: '第三方组件迁移未完全成功，请检查错误日志'
    });
  }

  if (migrationResult.stats.skippedFiles > 0) {
    recommendations.push({
      type: 'warning',
      message: `有 ${migrationResult.stats.skippedFiles} 个文件被跳过，可能需要手动处理`
    });
  }

  if (migrationResult.stats.aiMigratedFiles > 0) {
    recommendations.push({
      type: 'info',
      message: `${migrationResult.stats.aiMigratedFiles} 个文件通过AI迁移，建议仔细测试功能`
    });
  }

  if (migrationResult.stats.ruleMigratedFiles > 0) {
    recommendations.push({
      type: 'success',
      message: `${migrationResult.stats.ruleMigratedFiles} 个文件通过规则引擎迁移，相对可靠`
    });
  }

  if (migrationResult.stats.errors.length > 0) {
    recommendations.push({
      type: 'action',
      message: '存在迁移错误，请查看详细日志并手动修复',
      details: migrationResult.stats.errors.map(e => `${e.file}: ${e.error}`)
    });
  }

  return recommendations;
}

/**
 * 检查项目是否需要第三方组件迁移
 */
async function shouldRunThirdPartyMigration(projectPath) {
  try {
    const ComponentSearcher = require('./ComponentSearcher');
    const searcher = new ComponentSearcher(projectPath, {
      useRipgrep: false, // 快速检查使用glob
      verbose: false
    });

    const result = await searcher.searchAllComponents();
    
    return {
      needed: result.components.length > 0,
      componentCount: result.components.length,
      fileCount: result.totalFiles,
      components: result.components.map(c => ({
        name: c.name,
        target: c.target,
        fileCount: c.files.length
      }))
    };
  } catch (error) {
    console.warn(chalk.yellow(`检查第三方组件迁移需求失败: ${error.message}`));
    return {
      needed: false,
      error: error.message
    };
  }
}

/**
 * 预检查第三方组件迁移
 */
async function precheckThirdPartyMigration(projectPath, options = {}) {
  console.log(chalk.gray('🔍 预检查第三方组件迁移需求...'));
  
  const check = await shouldRunThirdPartyMigration(projectPath);
  
  if (!check.needed) {
    console.log(chalk.green('✅ 无需第三方组件迁移'));
    return {
      needed: false,
      message: '项目中未发现需要迁移的第三方组件'
    };
  }

  console.log(chalk.blue(`📦 发现 ${check.componentCount} 个需要迁移的组件`));
  console.log(chalk.gray(`涉及 ${check.fileCount} 个文件`));
  
  if (options.verbose) {
    check.components.forEach(component => {
      console.log(chalk.gray(`  - ${component.name} → ${component.target} (${component.fileCount} 个文件)`));
    });
  }

  return {
    needed: true,
    ...check,
    message: `发现 ${check.componentCount} 个需要迁移的第三方组件`
  };
}

/**
 * 生成第三方组件迁移报告
 */
function generateMigrationReport(migrationResult, outputPath) {
  const report = {
    timestamp: new Date().toISOString(),
    success: migrationResult.success,
    message: migrationResult.message,
    statistics: migrationResult.stats,
    recommendations: generateRecommendations(migrationResult),
    details: {
      processedFiles: migrationResult.stats.processedFiles,
      aiMigratedFiles: migrationResult.stats.aiMigratedFiles,
      ruleMigratedFiles: migrationResult.stats.ruleMigratedFiles,
      skippedFiles: migrationResult.stats.skippedFiles,
      errors: migrationResult.stats.errors
    }
  };

  if (outputPath) {
    const fs = require('fs-extra');
    fs.writeJsonSync(outputPath, report, { spaces: 2 });
    console.log(chalk.gray(`迁移报告已保存到: ${outputPath}`));
  }

  return report;
}

/**
 * 与BuildFixer集成的示例
 */
async function integrateWithBuildFixer(projectPath, buildErrors, options = {}) {
  console.log(chalk.blue('🔗 与BuildFixer集成执行第三方组件迁移'));
  
  // 检查构建错误是否与第三方组件相关
  const isThirdPartyRelated = buildErrors.some(error => 
    error.includes('vue-count-to') ||
    error.includes('vuedraggable') ||
    error.includes('vue-splitpane') ||
    error.includes('vue-template-compiler') ||
    error.includes('Cannot resolve module')
  );

  if (!isThirdPartyRelated) {
    return {
      success: true,
      message: '构建错误与第三方组件无关，跳过迁移',
      skipped: true
    };
  }

  console.log(chalk.yellow('检测到与第三方组件相关的构建错误，尝试迁移修复'));
  
  return await integrateWithAutoMigrator(projectPath, {
    ...options,
    reason: 'build-error-fix'
  });
}

/**
 * 批量项目迁移支持
 */
async function batchMigrateProjects(projectPaths, options = {}) {
  console.log(chalk.blue(`🚀 批量迁移 ${projectPaths.length} 个项目`));
  
  const results = [];
  
  for (let i = 0; i < projectPaths.length; i++) {
    const projectPath = projectPaths[i];
    const projectName = path.basename(projectPath);
    
    console.log(chalk.blue(`\n[${i + 1}/${projectPaths.length}] 处理项目: ${projectName}`));
    
    try {
      const result = await integrateWithAutoMigrator(projectPath, options);
      results.push({
        projectPath,
        projectName,
        ...result
      });
      
      if (result.success) {
        console.log(chalk.green(`✅ ${projectName} 迁移成功`));
      } else {
        console.log(chalk.red(`❌ ${projectName} 迁移失败: ${result.message}`));
      }
    } catch (error) {
      console.log(chalk.red(`❌ ${projectName} 迁移异常: ${error.message}`));
      results.push({
        projectPath,
        projectName,
        success: false,
        message: error.message,
        error: error
      });
    }
  }

  // 生成批量迁移报告
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  console.log(chalk.blue('\n📊 批量迁移结果:'));
  console.log(`成功: ${successCount}/${results.length}`);
  console.log(`失败: ${failureCount}/${results.length}`);
  
  if (failureCount > 0) {
    console.log(chalk.red('\n失败的项目:'));
    results.filter(r => !r.success).forEach(result => {
      console.log(chalk.red(`  - ${result.projectName}: ${result.message}`));
    });
  }

  return {
    totalProjects: results.length,
    successCount,
    failureCount,
    results
  };
}

module.exports = {
  integrateWithAutoMigrator,
  shouldRunThirdPartyMigration,
  precheckThirdPartyMigration,
  generateMigrationReport,
  integrateWithBuildFixer,
  batchMigrateProjects,
  generateRecommendations
};
